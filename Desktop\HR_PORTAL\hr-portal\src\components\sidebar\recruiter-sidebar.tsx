import { Link, useLocation } from "react-router-dom";
import {
  Calendar,
  Home,
  ClipboardList,
  UserPlus,
  FileText,
  BarChart2,
  UserCheck,
  Lock,
  LogOut,
  MessageSquare,
  Users,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    items: [
      { title: "Dashboard", url: "/recruiter/dashboard", icon: Home },
    ]
  },
  {
    title: "RECRUITMENT",
    items: [
      { title: "Assigned Requirements", url: "/recruiter/requirements", icon: ClipboardList },
    ]
  },
  {
    title: "CANDIDATES",
    items: [
      { title: "Register Candidate", url: "/register-candidate", icon: UserPlus },
      { title: "Peer Assigned Profiles", url: "/recruiter/peer-assigned-profiles", icon: FileText },
    ]
  },
  {
    title: "GENERAL",
    items: [
      { title: "Analytics", url: "/recruiter/analytics", icon: BarChart2 },
      { title: "Profile Analysis", url: "/recruiter/profile-analysis", icon: UserCheck },
      { title: "Candidate Messages", url: "/recruiter/messages", icon: MessageSquare },
      { title: "Calendar", url: "/calendar", icon: Calendar },
      { title: "Help and Support", url: "/help-and-support", icon: Users },
      { title: "Change Password", url: "/change-password", icon: Lock },
      { title: "Logout", url: "/logout", icon: LogOut },
    ]
  }
];

export function RecruiterSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <Sidebar className="bg-[var(--sidebar)] text-[var(--sidebar-foreground)] w-52 border-r border-[var(--sidebar-border)]">
      <SidebarHeader className="p-4 border-b border-[var(--sidebar-border)]">
        <h2 className="text-lg font-semibold text-white">Recruiter</h2>
      </SidebarHeader>

      <SidebarContent className="flex-1 overflow-y-auto">
        {menuSections.map((section, sectionIndex) => (
          <SidebarGroup key={sectionIndex} className="mb-1">
            {section.title && (
              <SidebarGroupLabel className="px-1 py-1 text-xs font-semibold text-[var(--sidebar-text-secondary)] uppercase tracking-wider">
                {section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu className="space-y-0">
                {section.items.map((item) => {
                  const isActive = currentPath === item.url;

                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        className={`w-full rounded-md px-1 py-1 text-sm font-medium transition-all duration-200 ${
                          isActive
                            ? "bg-[var(--sidebar-primary)] text-[var(--sidebar-primary-foreground)] shadow-sm"
                            : "text-[var(--sidebar-foreground)] hover:bg-[var(--sidebar-hover)] hover:text-white"
                        }`}
                      >
                        <Link to={item.url} className="flex items-center">
                          <item.icon
                            className={`h-4 w-4 mr-2 ${
                              isActive
                                ? "text-white"
                                : "text-[var(--sidebar-text-secondary)]"
                            }`}
                          />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  );
}
