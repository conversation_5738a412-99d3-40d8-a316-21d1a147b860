import { useState, useEffect } from "react";
import {
  Search,
  FileText,
  UserPlus,
  Edit,
  Trash2,
  Plus,
  RefreshCw,
} from "lucide-react";
import { KebabMenu, createKebabMenuItem, type KebabMenuItem } from "@/components/ui/kebab-menu";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useUser } from "@/contexts/user-context";

// Define the Job type based on API response
interface Job {
  id: number;
  budget_max: string;
  budget_min: string;
  client: string;
  contract_in_months: number | null;
  country: string;
  custom_job_type: string | null;
  data_updated_date: string | null;
  data_updated_time: string | null;
  date_created: string;
  detailed_jd: string;
  experience_max: string;
  experience_min: string;
  jd_pdf_extension: string | null;
  jd_pdf_present: boolean;
  job_status: "Active" | "Closed" | "On Hold";
  job_type: string;
  location: string;
  management: string;
  mode: string;
  no_of_positions: string;
  notice_period: string;
  recruiter: string;
  role: string;
  shift_timings: string;
  skills: string;
  time_created: string;
}

// API Response type
interface JobsApiResponse {
  job_posts_active: Job[];
  job_posts_closed: Job[];
  job_posts_hold: Job[];
  user_name: string;
}

// Define column configuration
interface Column {
  key: keyof Job | "posted_by";
  label: string;
  sortable: boolean;
}

export function JobListing() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <JobListingTable />
      </div>
    </div>
  );
}

function JobListingTable() {
  const { userEmail, userRole, userName } = useUser();

  const columns: Column[] = [
    { key: "id", label: "Job ID", sortable: true },
    { key: "date_created", label: "Date Created", sortable: true },
    { key: "job_status", label: "Status", sortable: true },
    { key: "client", label: "Client", sortable: true },
    { key: "posted_by", label: "Posted By", sortable: true },
    { key: "recruiter", label: "Recruiter", sortable: true },
    { key: "role", label: "Role", sortable: true },
    { key: "no_of_positions", label: "Positions", sortable: true },
  ];

  // State for API data
  const [jobsData, setJobsData] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [postedBy, setPostedBy] = useState<string>("");

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  // State for sorting
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Job | "posted_by" | null;
    direction: "ascending" | "descending" | null;
  }>({
    key: null,
    direction: null,
  });

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Fetch jobs data from API
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        console.log("Fetching jobs for user:", userName || userEmail);

        const API_BASE_URL = "https://backend.makonissoft.com";
        const requestBody = {
          username: userName || userEmail || "managerone",
        };

        console.log(
          "API Request:",
          `${API_BASE_URL}/view_all_jobs`,
          requestBody
        );

        const response = await fetch(`${API_BASE_URL}/view_all_jobs`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        console.log("API Response status:", response.status);

        if (!response.ok) {
          throw new Error(
            `Failed to fetch jobs: ${response.status} ${response.statusText}`
          );
        }

        const data: JobsApiResponse = await response.json();
        console.log("API Response data:", data);

        // Combine all job arrays
        const allJobs = [
          ...data.job_posts_active,
          ...data.job_posts_closed,
          ...data.job_posts_hold,
        ];

        console.log("Combined jobs:", allJobs.length, "jobs found");

        setJobsData(allJobs);
        setPostedBy(data.user_name);
        setError(null);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to fetch jobs";
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    // Always try to fetch, even without user context for testing
    fetchJobs();
  }, [userName, userEmail]);

  // Filter jobs based on search term and status
  const filteredJobs = jobsData.filter((job) => {
    // Check if job matches search term
    const matchesSearch =
      searchTerm === "" ||
      Object.values(job).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      ) ||
      postedBy.toLowerCase().includes(searchTerm.toLowerCase());

    // Check if job matches status filter
    const matchesStatus =
      statusFilter === "" || job.job_status === statusFilter;

    // Return true only if both conditions are met
    return matchesSearch && matchesStatus;
  });

  // Sort jobs if sort config is set
  const sortedJobs = [...filteredJobs].sort((a, b) => {
    if (!sortConfig.key) return 0;

    let aValue: any;
    let bValue: any;

    if (sortConfig.key === "posted_by") {
      aValue = postedBy;
      bValue = postedBy;
      return 0; // Same value for all rows
    } else {
      aValue = a[sortConfig.key as keyof Job];
      bValue = b[sortConfig.key as keyof Job];
    }

    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current jobs for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentJobs = sortedJobs.slice(indexOfFirstItem, indexOfLastItem);

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: keyof Job | "posted_by") => {
    // Apply a small shake animation to the table when sorting
    await animateSorting();

    let direction: "ascending" | "descending" | null = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }

    setSortConfig({ key, direction });
  };

  // Get status badge color
  const getStatusColor = (status: Job["job_status"]) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Closed":
        return "bg-red-100 text-red-800";
      case "On Hold":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Handle actions
  const handleAddCandidate = (jobId: string) => {
    alert(`Add candidate for job ${jobId}`);
  };

  const handleViewJD = (jobId: string) => {
    alert(`View JD for job ${jobId}`);
  };

  const handleEditJob = (jobId: string) => {
    alert(`Edit job ${jobId}`);
  };

  const handleDeleteJob = (jobId: string) => {
    alert(`Delete job ${jobId}`);
  };

  // Create kebab menu items for each job
  const createJobMenuItems = (job: Job): KebabMenuItem[] => [
    createKebabMenuItem(
      "add-candidate",
      "Add Candidate",
      () => handleAddCandidate(String(job.id)),
      { icon: UserPlus }
    ),
    createKebabMenuItem(
      "view-jd",
      "View Job Description",
      () => handleViewJD(String(job.id)),
      { icon: FileText, separator: true }
    ),
    createKebabMenuItem(
      "edit-job",
      "Edit Job",
      () => handleEditJob(String(job.id)),
      { icon: Edit }
    ),
    createKebabMenuItem(
      "delete-job",
      "Delete Job",
      () => {
        if (window.confirm(`Are you sure you want to delete job ${job.id}?`)) {
          handleDeleteJob(String(job.id));
        }
      },
      { icon: Trash2, variant: "destructive" }
    ),
  ];

  return (
    <div className="flex flex-col">
      {/* API Status Message */}
      {error && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center">
            <div className="text-yellow-800 text-sm">
              <strong>API Connection Issue:</strong> {error}
              <br />
              <span className="text-yellow-600">
                Showing sample data. Click refresh to load live data from the
                server.
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="mb-4 flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md block w-full pl-10 p-2.5 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search jobs..."
            value={searchTerm}
            onChange={async (e) => {
              const newSearchTerm = e.target.value;
              // Only animate if the search term is changing significantly
              if (Math.abs(newSearchTerm.length - searchTerm.length) > 2) {
                await animateSorting(); // Use the sorting animation for search too
              }
              setSearchTerm(newSearchTerm);
              setCurrentPage(1); // Reset to first page when searching
            }}
          />
        </div>
        <div className="flex items-center gap-3">
          {/* Status Filter */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animateSorting(); // Use the sorting animation for filtering too
              setStatusFilter(e.target.value);
              setCurrentPage(1); // Reset to first page when filtering
            }}
            value={statusFilter}
          >
            <option value="">All Status</option>
            <option value="Active">Active</option>
            <option value="Closed">Closed</option>
            <option value="On Hold">On Hold</option>
          </select>

          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animatePagination();
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            value={itemsPerPage}
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
          </select>
        </div>
      </div>

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-x-auto overflow-y-auto h-[485px]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Header */}
                <th className="sticky top-0 z-10 px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td
                    colSpan={columns.length + 4}
                    className="px-3 py-4 text-center text-sm text-gray-500"
                  >
                    Loading jobs...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td
                    colSpan={columns.length + 4}
                    className="px-3 py-4 text-center text-sm text-red-500"
                  >
                    Error: {error}
                  </td>
                </tr>
              ) : currentJobs.length > 0 ? (
                currentJobs.map((job, index) => (
                  <AnimatedTableRow key={job.id} index={index}>
                    {columns.map((column) => (
                      <td
                        key={`${job.id}-${String(column.key)}`}
                        className={`px-3 py-2 text-xs text-gray-800 font-medium ${
                          column.key === "role" || column.key === "recruiter"
                            ? "max-w-[150px] truncate"
                            : "whitespace-nowrap"
                        }`}
                      >
                        {column.key === "job_status" ? (
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${getStatusColor(
                              job.job_status
                            )}`}
                          >
                            {job.job_status}
                          </span>
                        ) : column.key === "posted_by" ? (
                          <span className="font-semibold text-blue-600">
                            {job.management}
                          </span>
                        ) : column.key === "client" ? (
                          <span className="font-semibold text-green-700">
                            {job.client}
                          </span>
                        ) : column.key === "role" ? (
                          <span
                            title={job.role}
                            className="truncate block font-medium max-w-[150px]"
                          >
                            {job.role}
                          </span>
                        ) : column.key === "recruiter" ? (
                          <span
                            title={job.recruiter}
                            className="truncate block font-medium max-w-[150px]"
                          >
                            {job.recruiter}
                          </span>
                        ) : (
                          String(job[column.key as keyof Job] || "")
                        )}
                      </td>
                    ))}

                    {/* Actions Column */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-center">
                      <KebabMenu items={createJobMenuItems(job)} />
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      columns.length + 1
                    } /* Add 1 for the actions column */
                    className="px-3 py-4 text-center text-sm text-gray-500"
                  >
                    No jobs found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-2 text-sm text-gray-700">
        <div>
          Showing {indexOfFirstItem + 1} to{" "}
          {Math.min(indexOfLastItem, filteredJobs.length)} of{" "}
          {filteredJobs.length} jobs
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredJobs.length / itemsPerPage)}
          onPageChange={paginate}
        />
      </div>
    </div>
  );
}
