import { useState, useEffect } from "react";
import {
  Search,
  Calendar,
  MessageSquare,
  FileText,
  Info,
  RefreshCw,
  Edit,
  Paperclip,
  Trash2,
} from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useCachedCandidates } from "@/hooks/use-cached-candidates";
import { useUser } from "@/contexts/user-context";
import { type Candidate, type Column } from "@/types/candidate";
import { CandidateDetailsModal } from "@/components/modals/candidate-details-modal";
import { ScheduleMeetingModal } from "@/components/modals/schedule-meeting-modal";
import { EditCandidateModal } from "@/components/modals/edit-candidate-modal";
import { UpdateCandidateModal } from "@/components/modals/update-candidate-modal";
import { openWhatsAppChat, WhatsAppTemplates } from "@/utils/whatsapp";
import { KebabMenu, createKebabMenuItem, type KebabMenuItem } from "@/components/ui/kebab-menu";




export function DashboardCandidateTable() {
  const { userEmail, userRole, userName } = useUser();

  // Use cached candidates data with automatic fallback
  const {
    candidates: dataSource,
    loading: apiLoading,
    error: apiError,
    forceRefresh,
    lastFetched,
    hasCache,
    isCacheValid,
    isUsingFallbackData,
  } = useCachedCandidates();

  const columns: Column[] = [
    { key: "appliedDate", label: "Date", sortable: true },
    { key: "jobId", label: "Job ID", sortable: true },
    { key: "firstName", label: "Name", sortable: true },
    { key: "email", label: "Email", sortable: true },
    { key: "phone", label: "Mobile", sortable: true },
    { key: "client", label: "Client", sortable: true },
    { key: "profile", label: "Profile", sortable: true },
    { key: "skills", label: "Skills", sortable: true },
    { key: "status", label: "Status", sortable: true },
    { key: "lastUpdated", label: "Last Updated", sortable: true },
    { key: "comment", label: "Comment", sortable: true },
    { key: "peerReviewer", label: "Peer Reviewer", sortable: true },
    { key: "recruiter", label: "Recruiter", sortable: true },
  ];



  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  // State for sorting
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Candidate | null;
    direction: "ascending" | "descending" | null;
  }>({
    key: null,
    direction: null,
  });

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Modal states
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isMeetingModalOpen, setIsMeetingModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  // Function to open candidate details modal
  const openCandidateDetails = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsDetailsModalOpen(true);
  };

  // Function to close candidate details modal
  const closeCandidateDetails = () => {
    setSelectedCandidate(null);
    setIsDetailsModalOpen(false);
  };

  // Function to open schedule meeting modal
  const openScheduleMeeting = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsMeetingModalOpen(true);
  };

  // Function to close schedule meeting modal
  const closeScheduleMeeting = () => {
    setSelectedCandidate(null);
    setIsMeetingModalOpen(false);
  };

  // Function to open edit candidate modal
  const openEditCandidate = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsEditModalOpen(true);
  };

  // Function to close edit candidate modal
  const closeEditCandidate = () => {
    setSelectedCandidate(null);
    setIsEditModalOpen(false);
  };

  // Function to handle candidate update
  const handleCandidateUpdate = (updatedCandidate: Candidate) => {
    // Here you would typically update the candidate in your data store
    console.log('Updated candidate:', updatedCandidate);
    // For now, we'll just close the modal
    closeEditCandidate();
  };

  // Function to open update candidate status modal
  const openUpdateCandidate = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsUpdateModalOpen(true);
  };

  // Function to close update candidate status modal
  const closeUpdateCandidate = () => {
    setSelectedCandidate(null);
    setIsUpdateModalOpen(false);
  };

  // Function to handle candidate status update
  const handleCandidateStatusUpdate = (candidateId: string, status: string, comment: string) => {
    // Here you would typically update the candidate status in your data store
    console.log('Updated candidate status:', { candidateId, status, comment });
    // For now, we'll just close the modal
    closeUpdateCandidate();
  };

  // Function to open WhatsApp chat
  const openWhatsAppWithCandidate = (candidate: Candidate) => {
    if (candidate.phone) {
      const message = WhatsAppTemplates.general(`${candidate.firstName} ${candidate.lastName}`);
      openWhatsAppChat(candidate.phone, message);
    } else {
      alert('No phone number available for this candidate');
    }
  };

  // Create kebab menu items for each candidate
  const createCandidateMenuItems = (candidate: Candidate): KebabMenuItem[] => [
    createKebabMenuItem(
      "schedule-meeting",
      "Schedule Meeting",
      () => openScheduleMeeting(candidate),
      { icon: Calendar }
    ),
    createKebabMenuItem(
      "whatsapp-chat",
      "Chat on WhatsApp",
      () => openWhatsAppWithCandidate(candidate),
      { icon: MessageSquare }
    ),
    createKebabMenuItem(
      "view-resume",
      "View Resume",
      () => alert('Resume viewing functionality to be implemented'),
      { icon: FileText }
    ),
    createKebabMenuItem(
      "view-details",
      "View Details",
      () => openCandidateDetails(candidate),
      { icon: Info, separator: true }
    ),
    createKebabMenuItem(
      "update-status",
      "Update Status",
      () => openUpdateCandidate(candidate),
      { icon: RefreshCw }
    ),
    createKebabMenuItem(
      "edit-candidate",
      "Edit Candidate",
      () => openEditCandidate(candidate),
      { icon: Edit }
    ),
    createKebabMenuItem(
      "additional-files",
      "Additional Files",
      () => alert('Additional Files functionality to be implemented'),
      { icon: Paperclip, separator: true }
    ),
    createKebabMenuItem(
      "delete",
      "Delete",
      () => {
        if (window.confirm('Are you sure you want to delete this candidate?')) {
          alert('Delete functionality to be implemented');
        }
      },
      { icon: Trash2, variant: "destructive" }
    ),
  ];

  // Filter candidates based on search term and status
  const filteredCandidates = dataSource.filter((candidate) => {
    // Check if candidate matches search term
    const matchesSearch =
      searchTerm === "" ||
      Object.values(candidate).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      );

    // Check if candidate matches status filter
    const matchesStatus =
      statusFilter === "" || candidate.status === statusFilter;

    // Return true only if both conditions are met
    return matchesSearch && matchesStatus;
  });

  // Sort candidates if sort config is set
  const sortedCandidates = [...filteredCandidates].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current candidates for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCandidates = sortedCandidates.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  // Calculate total pages
  const totalPages = Math.ceil(filteredCandidates.length / itemsPerPage);

  // Ensure current page is valid
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [currentPage, totalPages]);

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: keyof Candidate) => {
    // Apply a small shake animation to the table when sorting
    await animateSorting();

    let direction: "ascending" | "descending" | null = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }

    setSortConfig({ key, direction });
  };

  // Get status badge color
  const getStatusColor = (status: Candidate["status"]) => {
    switch (status) {
      case "New":
        return "bg-blue-100 text-blue-800";
      case "Screening":
        return "bg-purple-100 text-purple-800";
      case "Interview":
        return "bg-yellow-100 text-yellow-800";
      case "Offer":
        return "bg-orange-100 text-orange-800";
      case "Hired":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-3.5 w-3.5 text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md block w-full pl-10 p-2.5 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search candidates..."
            value={searchTerm}
            onChange={async (e) => {
              const newSearchTerm = e.target.value;
              // Only animate if the search term is changing significantly
              if (Math.abs(newSearchTerm.length - searchTerm.length) > 2) {
                await animateSorting(); // Use the sorting animation for search too
              }
              setSearchTerm(newSearchTerm);
              setCurrentPage(1); // Reset to first page when searching
            }}
          />
        </div>
        <div className="flex items-center gap-3">
          {/* Refresh Button */}
          <button
            onClick={forceRefresh}
            disabled={apiLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2.5 rounded-md text-sm font-medium flex items-center gap-2"
            title={`Refresh data${lastFetched ? ` (Last updated: ${lastFetched.toLocaleTimeString()})` : ''}`}
          >
            <RefreshCw className={`h-3.5 w-3.5 ${apiLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>

          {/* Status Filter */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animateSorting(); // Use the sorting animation for filtering too
              setStatusFilter(e.target.value);
              setCurrentPage(1); // Reset to first page when filtering
            }}
            value={statusFilter}
          >
            <option value="">All Status</option>
            <option value="New">New</option>
            <option value="Screening">Screening</option>
            <option value="Interview">Interview</option>
            <option value="Offer">Offer</option>
            <option value="Hired">Hired</option>
            <option value="Rejected">Rejected</option>
          </select>

          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              const newItemsPerPage = Number(e.target.value);
              await animatePagination();
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
              console.log('Items per page changed to:', newItemsPerPage);
            }}
            value={itemsPerPage}
          >
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>
        </div>
      </div>

      {/* Cache Status */}
      {/* {hasCache && lastFetched && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Info className="h-3.5 w-3.5 text-green-600 mr-2" />
              <p className="text-sm text-green-800">
                Data cached at {lastFetched.toLocaleTimeString()}
                {!isCacheValid && <span className="text-orange-600 ml-2">(Cache expired - will refresh on next action)</span>}
              </p>
            </div>
            <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
              {dataSource.length} candidates
            </span>
          </div>
        </div>
      )} */}

      {/* Fallback Data Warning */}
      {isUsingFallbackData && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center">
            <Info className="h-3.5 w-3.5 text-blue-600 mr-2" />
            <p className="text-sm text-blue-800">
              Showing sample data. Click refresh to load live data from the server.
            </p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {apiError && (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center">
            <Info className="h-3.5 w-3.5 text-yellow-600 mr-2" />
            <p className="text-sm text-yellow-800">{apiError}</p>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {apiLoading && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center">
            <RefreshCw className="h-3.5 w-3.5 text-blue-600 mr-2 animate-spin" />
            <p className="text-sm text-blue-800">Loading candidates...</p>
          </div>
        </div>
      )}

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading || apiLoading}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-auto h-[490px]" style={{ scrollbarGutter: 'stable' }}>
          <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className={`sticky top-0 z-10 px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200 ${column.key === "comment" ? "w-36" :
                        column.key === "skills" ? "w-24" :
                          column.key === "email" ? "w-36" :
                            column.key === "firstName" ? "w-28" :
                              column.key === "peerReviewer" || column.key === "recruiter" ? "w-28" :
                                "w-24"

                      }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Headers */}
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentCandidates.length > 0 ? (
                currentCandidates.map((candidate, index) => (
                  <AnimatedTableRow key={candidate.id} index={index}>
                    {columns.map((column) => (
                      <td
                        key={`${candidate.id}-${String(column.key)}`}
                        className={`px-3 py-2 text-xs text-gray-800 font-medium ${column.key === "comment" || column.key === "skills" ? "max-w-xs truncate" : "whitespace-nowrap"
                          }`}
                      >
                        {column.key === "status" ? (
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${getStatusColor(
                              candidate.status
                            )}`}
                          >
                            {candidate.status}
                          </span>
                        ) : column.key === "firstName" ? (
                          <span className="font-semibold text-green-700">
                            {candidate.firstName} {candidate.lastName}
                          </span>
                        ) : column.key === "comment" ? (
                          <span title={candidate.comment} className="truncate block font-medium">
                            {candidate.comment}
                          </span>
                        ) : column.key === "skills" ? (
                          <span title={candidate.skills} className="truncate block font-medium">
                            {candidate.skills}
                          </span>
                        ) : column.key === "peerReviewer" ? (
                          <span className="font-semibold text-blue-600">
                            {candidate.peerReviewer}
                          </span>
                        ) : column.key === "recruiter" ? (
                          <span className="font-semibold text-green-600">
                            {candidate.recruiter}
                          </span>
                        ) : (
                          String(candidate[column.key])
                        )}
                      </td>
                    ))}

                    {/* Actions Column */}
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500 w-16">
                      <div className="flex items-center justify-center">
                        <KebabMenu items={createCandidateMenuItems(candidate)} />
                      </div>
                    </td>


                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      columns.length + 1
                    } /* Add 1 for the actions column */
                    className="px-3 py-3 text-center text-sm text-gray-500"
                  >
                    No candidates found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          {filteredCandidates.length > 0 ? (
            <>
              Showing {indexOfFirstItem + 1} to{" "}
              {Math.min(indexOfLastItem, filteredCandidates.length)} of{" "}
              {filteredCandidates.length} candidates
            </>
          ) : (
            "No candidates found"
          )}
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={paginate}
        />
      </div>

      {/* Candidate Details Modal */}
      <CandidateDetailsModal
        candidate={selectedCandidate}
        isOpen={isDetailsModalOpen}
        onClose={closeCandidateDetails}
      />

      {/* Schedule Meeting Modal */}
      <ScheduleMeetingModal
        candidate={selectedCandidate}
        isOpen={isMeetingModalOpen}
        onClose={closeScheduleMeeting}
      />

      {/* Edit Candidate Modal */}
      <EditCandidateModal
        candidate={selectedCandidate}
        isOpen={isEditModalOpen}
        onClose={closeEditCandidate}
        onSave={handleCandidateUpdate}
      />

      {/* Update Candidate Status Modal */}
      <UpdateCandidateModal
        candidate={selectedCandidate}
        isOpen={isUpdateModalOpen}
        onClose={closeUpdateCandidate}
        onUpdate={handleCandidateStatusUpdate}
      />
    </div>
  );
}
