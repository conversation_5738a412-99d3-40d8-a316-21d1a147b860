import { useState, useEffect } from "react";
import {
  Search,
  CheckCircle,
  UserX,
  Trash2,
  Plus,
  User,
} from "lucide-react";
import { KebabMenu, createKebabMenuItem, type KebabMenuItem } from "@/components/ui/kebab-menu";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ApiService, type ActiveUser } from "@/services/api";

// Define the User type with required properties
interface User {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: "Manager" | "Recruiter";
  isVerified: boolean;
  isActive: boolean;
  isPeerReviewer: boolean;
}

// Function to convert API user to local User interface
const convertApiUserToLocal = (apiUser: ActiveUser): User => {
  const nameParts = apiUser.name.split(' ');
  return {
    id: apiUser.id,
    username: apiUser.username,
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || '',
    email: apiUser.email,
    userType: apiUser.user_type === 'management' ? 'Manager' : 'Recruiter',
    isVerified: apiUser.is_verified,
    isActive: apiUser.is_active,
    isPeerReviewer: apiUser.peer_reviewer_status,
  };
};

// Define column configuration
interface Column {
  key: keyof User;
  label: string;
  sortable: boolean;
}

export function UserAccounts() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <UserAccountsTable />
      </div>
    </div>
  );
}

// Interface for new user form
interface NewUserFormData {
  username: string;
  name: string;
  email: string;
  userType: "Manager" | "Recruiter" | "";
}

function UserAccountsTable() {
  const columns: Column[] = [
    { key: "username", label: "Username", sortable: true },
    { key: "firstName", label: "Name", sortable: true },
    { key: "email", label: "Email", sortable: true },
    { key: "userType", label: "User Type", sortable: true },
  ];

  // State for API data
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [userTypeFilter, setUserTypeFilter] = useState("");
  const [verificationFilter, setVerificationFilter] = useState("");

  // State for new user dialog
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState<NewUserFormData>({
    username: "",
    name: "",
    email: "",
    userType: "",
  });

  // State for sorting
  const [sortConfig, setSortConfig] = useState<{
    key: keyof User | null;
    direction: "ascending" | "descending" | null;
  }>({
    key: null,
    direction: null,
  });

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await ApiService.fetchActiveUsers();
        const convertedUsers = response.active_users_manager.map(convertApiUserToLocal);
        setUsers(convertedUsers);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch users');
        console.error('Failed to fetch users:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Filter users based on search term and filters
  const filteredUsers = users.filter((user) => {
    // Check if user matches search term
    const matchesSearch =
      searchTerm === "" ||
      Object.values(user).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      );

    // Check if user matches user type filter
    const matchesUserType =
      userTypeFilter === "" || user.userType === userTypeFilter;

    // Check if user matches verification filter
    const matchesVerification =
      verificationFilter === "" ||
      (verificationFilter === "Verified" && user.isVerified) ||
      (verificationFilter === "Unverified" && !user.isVerified);

    // Return true only if all conditions are met
    return matchesSearch && matchesUserType && matchesVerification;
  });

  // Sort users if sort config is set
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current users for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUsers = sortedUsers.slice(indexOfFirstItem, indexOfLastItem);

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: keyof User) => {
    // Apply a small shake animation to the table when sorting
    await animateSorting();

    let direction: "ascending" | "descending" | null = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }

    setSortConfig({ key, direction });
  };

  // Handle input change for new user form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewUserData({
      ...newUserData,
      [name]: value,
    });
  };

  // Handle user type selection
  const handleUserTypeChange = (value: string) => {
    setNewUserData({
      ...newUserData,
      userType: value as "Manager" | "Recruiter" | "",
    });
  };

  // Handle add new user
  const handleAddNewUser = () => {
    setIsDialogOpen(true);
  };

  // Handle form submission
  const handleSubmitNewUser = () => {
    // Validate form
    if (
      !newUserData.username ||
      !newUserData.name ||
      !newUserData.email ||
      !newUserData.userType
    ) {
      alert("Please fill in all fields");
      return;
    }

    // In a real app, you would send this data to an API
    console.log("Creating new user:", newUserData);

    // For demo purposes, show success message
    alert(`New user ${newUserData.name} created successfully!`);

    // Reset form and close dialog
    setNewUserData({
      username: "",
      name: "",
      email: "",
      userType: "",
    });
    setIsDialogOpen(false);
  };

  // Handle toggle actions
  const handleToggleVerification = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'verify');
      // Update local state
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id ? { ...u, isVerified: newStatus } : u
        )
      );
    } catch (error) {
      console.error('Failed to update verification status:', error);
      alert('Failed to update verification status');
    }
  };

  const handleToggleActivation = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'deactivate');
      // Update local state
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id ? { ...u, isActive: newStatus } : u
        )
      );
    } catch (error) {
      console.error('Failed to update activation status:', error);
      alert('Failed to update activation status');
    }
  };

  const handleTogglePeerReviewer = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'peer_reviewer');
      // Update local state
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id ? { ...u, isPeerReviewer: newStatus } : u
        )
      );
    } catch (error) {
      console.error('Failed to update peer reviewer status:', error);
      alert('Failed to update peer reviewer status');
    }
  };

  const handleDeleteUser = (userId: number) => {
    alert(`Delete user ${userId}`);
  };

  // Create kebab menu items for each user
  const createUserMenuItems = (user: User): KebabMenuItem[] => [
    createKebabMenuItem(
      "toggle-verification",
      user.isVerified ? "Unverify User" : "Verify User",
      () => handleToggleVerification(user, !user.isVerified),
      { icon: CheckCircle, variant: user.isVerified ? "warning" : "default" }
    ),
    createKebabMenuItem(
      "toggle-activation",
      user.isActive ? "Deactivate User" : "Activate User",
      () => handleToggleActivation(user, !user.isActive),
      { icon: UserX, variant: user.isActive ? "warning" : "default" }
    ),
    createKebabMenuItem(
      "toggle-peer-reviewer",
      user.isPeerReviewer ? "Remove Peer Reviewer" : "Make Peer Reviewer",
      () => handleTogglePeerReviewer(user, !user.isPeerReviewer),
      { icon: User, separator: true }
    ),
    createKebabMenuItem(
      "delete-user",
      "Delete User",
      () => {
        if (window.confirm(`Are you sure you want to delete user ${user.username}?`)) {
          handleDeleteUser(user.id);
        }
      },
      { icon: Trash2, variant: "destructive" }
    ),
  ];

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md block w-full pl-10 p-2.5 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search users..."
            value={searchTerm}
            onChange={async (e) => {
              const newSearchTerm = e.target.value;
              // Only animate if the search term is changing significantly
              if (Math.abs(newSearchTerm.length - searchTerm.length) > 2) {
                await animateSorting(); // Use the sorting animation for search too
              }
              setSearchTerm(newSearchTerm);
              setCurrentPage(1); // Reset to first page when searching
            }}
          />
        </div>
        <div className="flex items-center gap-3">
          {/* User Type Filter */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animateSorting(); // Use the sorting animation for filtering too
              setUserTypeFilter(e.target.value);
              setCurrentPage(1); // Reset to first page when filtering
            }}
            value={userTypeFilter}
          >
            <option value="">All User Types</option>
            <option value="Manager">Manager</option>
            <option value="Recruiter">Recruiter</option>
          </select>

          {/* Verification Filter */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animateSorting(); // Use the sorting animation for filtering too
              setVerificationFilter(e.target.value);
              setCurrentPage(1); // Reset to first page when filtering
            }}
            value={verificationFilter}
          >
            <option value="">All Verification</option>
            <option value="Verified">Verified</option>
            <option value="Unverified">Unverified</option>
          </select>

          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animatePagination();
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            value={itemsPerPage}
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
          </select>

          {/* Add New User Button */}
          <Button
            onClick={handleAddNewUser}
            className="flex items-center justify-center py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm font-medium"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span>Add New User</span>
          </Button>

          {/* Add New User Dialog */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>New Account</DialogTitle>
                <DialogDescription>
                  Create a new user account. Fill in all fields to continue.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="username" className="text-right">
                    Username
                  </Label>
                  <Input
                    id="username"
                    name="username"
                    value={newUserData.username}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={newUserData.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={newUserData.email}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="userType" className="text-right">
                    User Type
                  </Label>
                  <Select
                    value={newUserData.userType}
                    onValueChange={handleUserTypeChange}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select user type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Manager">Manager</SelectItem>
                      <SelectItem value="Recruiter">Recruiter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="button" onClick={handleSubmitNewUser}>
                  Create Account
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-x-auto overflow-y-auto h-[475px]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Header */}
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentUsers.length > 0 ? (
                currentUsers.map((user, index) => (
                  <AnimatedTableRow key={user.id} index={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.username}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {`${user.firstName} ${user.lastName}`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.userType === "Manager"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        {user.userType}
                      </span>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <KebabMenu items={createUserMenuItems(user)} />
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      columns.length + 1
                    } /* Add 1 for the actions column */
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    {loading ? 'Loading users...' : error ? `Error: ${error}` : 'No users found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          Showing {indexOfFirstItem + 1} to{" "}
          {Math.min(indexOfLastItem, filteredUsers.length)} of{" "}
          {filteredUsers.length} users
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredUsers.length / itemsPerPage)}
          onPageChange={paginate}
        />
      </div>
    </div>
  );
}
